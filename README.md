# SmartPark Car Wash Management System

A comprehensive web-based Car Washing Sales Management System designed for SmartPark, a car service company in Rubavu District, Rwanda. This system digitizes the manual paper-based process for tracking car washing sales, service records, and payments.

## 🚀 Features

### Core Functionality
- **Car Management**: Register and manage vehicle information (plate numbers, types, sizes, driver details)
- **Service Packages**: Manage different car wash service packages with pricing
- **Service Recording**: Track car wash services with date, car, and package selection
- **Payment Processing**: Record and track payments with multiple payment methods
- **Bill Generation**: Generate detailed bills for services with print functionality
- **Daily Reports**: View daily service and payment summaries
- **Dashboard**: Overview of daily statistics and recent activities

### Technical Features
- **Authentication**: Session-based user authentication
- **Responsive Design**: Mobile-friendly interface using TailwindCSS v3
- **Real-time Updates**: Dynamic data updates without page refresh
- **Search & Filter**: Advanced search capabilities across all modules
- **Data Validation**: Comprehensive input validation on both frontend and backend
- **Error Handling**: User-friendly error messages and loading states

## 🛠 Technology Stack

### Frontend
- **React.js 18** - Modern JavaScript framework
- **TailwindCSS v3** - Utility-first CSS framework
- **React Router DOM** - Client-side routing
- **Axios** - HTTP client for API communication
- **React Hot Toast** - Toast notifications
- **Lucide React** - Beautiful icons

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **MySQL** - Relational database
- **MySQL2** - MySQL database driver
- **Express Session** - Session management
- **bcrypt** - Password hashing
- **CORS** - Cross-origin resource sharing

## 📋 Prerequisites

Before running this application, make sure you have the following installed:

- **Node.js** (v18 or higher)
- **MySQL** (v8 or higher) - XAMPP, WAMP, or standalone MySQL
- **npm** or **yarn** package manager

## 🚀 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd "CAR WASH MANAGEMENT SYSTEM"
```

### 2. Backend Setup
```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Update .env with your MongoDB connection string
MONGODB_URI=mongodb://localhost:27017/car_wash_management
```

### 3. Frontend Setup
```bash
# Navigate to frontend directory
cd ../frontend

# Install dependencies
npm install
```

### 4. Database Setup
```bash
# Start MySQL service
# XAMPP: Start MySQL from XAMPP Control Panel
# WAMP: Start MySQL from WAMP Manager
# Standalone: net start MySQL (Windows) or sudo systemctl start mysql (Linux)

# Import database schema
mysql -u root -p < database/mysql-schema.sql

# Or use the setup script
setup-database.bat
```

## 🏃‍♂️ Running the Application

### Development Mode

1. **Start the Backend Server**
```bash
cd backend
npm run dev
```
The backend will run on `http://localhost:5000`

2. **Start the Frontend Development Server**
```bash
cd frontend
npm start
```
The frontend will run on `http://localhost:3000`

### Production Mode

1. **Build the Frontend**
```bash
cd frontend
npm run build
```

2. **Start the Backend**
```bash
cd backend
npm start
```

## 🔐 Default Login Credentials

- **Username**: `admin`
- **Password**: `admin123`

## 📊 Database Schema

### Collections

1. **users** - System users with authentication
2. **cars** - Registered vehicles information
3. **packages** - Car wash service packages
4. **services** - Service records with car and package details
5. **payments** - Payment records linked to services

### Sample Data

The system comes with pre-populated sample data including:
- 5 service packages (Basic, Premium, Deluxe, Express, VIP)
- 5 sample cars with different types and sizes
- Sample service records and payments

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/check` - Check authentication status

### Cars Management
- `GET /api/cars` - Get all cars
- `POST /api/cars` - Add new car
- `GET /api/cars/:plateNumber` - Get specific car
- `PUT /api/cars/:plateNumber` - Update car
- `DELETE /api/cars/:plateNumber` - Delete car

### Packages Management
- `GET /api/packages` - Get all packages
- `POST /api/packages` - Add new package (admin only)
- `PUT /api/packages/:id` - Update package (admin only)
- `DELETE /api/packages/:id` - Delete package (admin only)

### Services Management
- `GET /api/services` - Get all services
- `POST /api/services` - Record new service
- `GET /api/services/:id` - Get specific service
- `PUT /api/services/:id` - Update service

### Payments Management
- `GET /api/payments` - Get all payments
- `POST /api/payments` - Record new payment
- `GET /api/payments/outstanding` - Get outstanding payments

### Reports
- `GET /api/reports/daily?date=YYYY-MM-DD` - Daily report
- `GET /api/reports/bills/:serviceId` - Generate bill

## 🎨 User Interface

### Pages
1. **Login** - User authentication
2. **Dashboard** - Overview and quick actions
3. **Car Management** - CRUD operations for vehicles
4. **Package Management** - Service package administration
5. **Service Recording** - Log car wash services
6. **Payment Processing** - Record and track payments
7. **Reports** - Daily and monthly reports
8. **Bill Generation** - Create printable bills

### Design Features
- Clean, professional interface
- Responsive design for desktop and tablet
- Intuitive navigation with breadcrumbs
- Loading states and error handling
- Toast notifications for user feedback
- Print-friendly bill layouts

## 🔧 Configuration

### Environment Variables

**Backend (.env)**
```env
MONGODB_URI=mongodb://localhost:27017/car_wash_management
DB_NAME=car_wash_management
PORT=5000
NODE_ENV=development
SESSION_SECRET=your_session_secret_here
SESSION_NAME=car_wash_session
BCRYPT_ROUNDS=10
```

**Frontend**
```env
REACT_APP_API_URL=http://localhost:5000/api
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] User can log in with valid credentials
- [ ] User can add, edit, and delete cars
- [ ] User can manage service packages
- [ ] User can record new services
- [ ] User can process payments
- [ ] User can generate and print bills
- [ ] User can view daily reports
- [ ] All forms validate input correctly
- [ ] Error messages display appropriately

## 🚀 Deployment

### Production Deployment Steps

1. **Prepare Environment**
   - Set up MongoDB Atlas or local MongoDB instance
   - Configure production environment variables
   - Set up SSL certificates (recommended)

2. **Build Application**
   ```bash
   cd frontend
   npm run build
   ```

3. **Deploy Backend**
   - Upload backend files to server
   - Install dependencies: `npm install --production`
   - Start with PM2: `pm2 start server.js`

4. **Deploy Frontend**
   - Upload build files to web server (Apache/Nginx)
   - Configure reverse proxy to backend API

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Support

For support and questions:
- Email: <EMAIL>
- Phone: +250 788 000 000
- Address: Rubavu District, Rwanda

## 🙏 Acknowledgments

- SmartPark team for requirements and testing
- Rwanda ICT development community
- Open source contributors

---

**SmartPark Car Wash Management System** - Digitizing car wash operations for efficient business management.
