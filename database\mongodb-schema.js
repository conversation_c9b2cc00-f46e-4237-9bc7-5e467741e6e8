// Car Washing Sales Management System Database Schema
// SmartPark Company - Rubavu District, Rwanda
// MongoDB Collections and Sample Data

// Database: car_wash_management

// 1. packages collection
db.packages.insertMany([
  {
    _id: ObjectId(),
    packageName: "Basic Wash",
    packageDescription: "Exterior hand wash only - Quick and efficient cleaning",
    packagePrice: 5000.00,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    packageName: "Premium Wash",
    packageDescription: "Exterior + interior cleaning - Complete car care",
    packagePrice: 8000.00,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    packageName: "Deluxe Wash",
    packageDescription: "Full service with wax - Premium treatment with protection",
    packagePrice: 12000.00,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    packageName: "Express Wash",
    packageDescription: "Quick exterior wash for busy customers",
    packagePrice: 3500.00,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    packageName: "VIP Service",
    packageDescription: "Complete detailing with premium products",
    packagePrice: 15000.00,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// 2. users collection
db.users.insertMany([
  {
    _id: ObjectId(),
    username: "admin",
    password: "$2b$10$rOzJqQZJqQZJqQZJqQZJqOzJqQZJqQZJqQZJqQZJqQZJqQZJqQZJq", // admin123
    role: "admin",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    username: "manager",
    password: "$2b$10$rOzJqQZJqQZJqQZJqQZJqOzJqQZJqQZJqQZJqQZJqQZJqQZJqQZJq", // admin123
    role: "manager",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// 3. cars collection
db.cars.insertMany([
  {
    _id: ObjectId(),
    plateNumber: "RAB-001A",
    carType: "Sedan",
    carSize: "Medium",
    driverName: "Jean Baptiste",
    phoneNumber: "+250788123456",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    plateNumber: "RAB-002B",
    carType: "SUV",
    carSize: "Large",
    driverName: "Marie Claire",
    phoneNumber: "+250788234567",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    plateNumber: "RAB-003C",
    carType: "Truck",
    carSize: "Large",
    driverName: "Paul Kagame",
    phoneNumber: "+250788345678",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    plateNumber: "RAB-004D",
    carType: "Sedan",
    carSize: "Small",
    driverName: "Grace Uwimana",
    phoneNumber: "+250788456789",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    plateNumber: "RAB-005E",
    carType: "Hatchback",
    carSize: "Small",
    driverName: "Eric Nzeyimana",
    phoneNumber: "+250788567890",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// 4. services collection
db.services.insertMany([
  {
    _id: ObjectId(),
    serviceDate: new Date("2025-01-15"),
    plateNumber: "RAB-001A",
    packageId: ObjectId(), // Reference to package
    packageName: "Basic Wash",
    packagePrice: 5000.00,
    carInfo: {
      driverName: "Jean Baptiste",
      carType: "Sedan",
      carSize: "Medium",
      phoneNumber: "+250788123456"
    },
    paymentStatus: "Paid",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    serviceDate: new Date("2025-01-15"),
    plateNumber: "RAB-002B",
    packageId: ObjectId(),
    packageName: "Premium Wash",
    packagePrice: 8000.00,
    carInfo: {
      driverName: "Marie Claire",
      carType: "SUV",
      carSize: "Large",
      phoneNumber: "+250788234567"
    },
    paymentStatus: "Paid",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// 5. payments collection
db.payments.insertMany([
  {
    _id: ObjectId(),
    serviceId: ObjectId(), // Reference to service
    amountPaid: 5000.00,
    paymentDate: new Date("2025-01-15"),
    paymentMethod: "Cash",
    plateNumber: "RAB-001A",
    packageName: "Basic Wash",
    packagePrice: 5000.00,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: ObjectId(),
    serviceId: ObjectId(),
    amountPaid: 8000.00,
    paymentDate: new Date("2025-01-15"),
    paymentMethod: "Cash",
    plateNumber: "RAB-002B",
    packageName: "Premium Wash",
    packagePrice: 8000.00,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Create indexes for better performance
db.cars.createIndex({ "plateNumber": 1 }, { unique: true });
db.cars.createIndex({ "carType": 1 });
db.cars.createIndex({ "carSize": 1 });
db.cars.createIndex({ "driverName": 1 });

db.packages.createIndex({ "packageName": 1 });
db.packages.createIndex({ "packagePrice": 1 });

db.services.createIndex({ "serviceDate": 1 });
db.services.createIndex({ "plateNumber": 1 });
db.services.createIndex({ "packageId": 1 });
db.services.createIndex({ "paymentStatus": 1 });

db.payments.createIndex({ "serviceId": 1 });
db.payments.createIndex({ "paymentDate": 1 });
db.payments.createIndex({ "plateNumber": 1 });

db.users.createIndex({ "username": 1 }, { unique: true });
