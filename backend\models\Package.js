const mongoose = require('mongoose');

const packageSchema = new mongoose.Schema({
  packageName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  packageDescription: {
    type: String,
    trim: true,
    maxlength: 500
  },
  packagePrice: {
    type: Number,
    required: true,
    min: 0
  }
}, {
  timestamps: true
});

// Index for better search performance
packageSchema.index({ packageName: 1 });
packageSchema.index({ packagePrice: 1 });

module.exports = mongoose.model('Package', packageSchema);
