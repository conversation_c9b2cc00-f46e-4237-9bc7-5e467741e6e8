import axios from 'axios';

// Create axios instance with default configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  withCredentials: true,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth headers or other request modifications here
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Redirect to login if unauthorized
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Authentication API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  checkAuth: () => api.get('/auth/check'),
};

// Cars API
export const carsAPI = {
  getAll: () => api.get('/cars'),
  getById: (plateNumber) => api.get(`/cars/${plateNumber}`),
  create: (carData) => api.post('/cars', carData),
  update: (plateNumber, carData) => api.put(`/cars/${plateNumber}`, carData),
  delete: (plateNumber) => api.delete(`/cars/${plateNumber}`),
};

// Packages API
export const packagesAPI = {
  getAll: () => api.get('/packages'),
  getById: (id) => api.get(`/packages/${id}`),
  create: (packageData) => api.post('/packages', packageData),
  update: (id, packageData) => api.put(`/packages/${id}`, packageData),
  delete: (id) => api.delete(`/packages/${id}`),
};

// Services API
export const servicesAPI = {
  getAll: () => api.get('/services'),
  getById: (id) => api.get(`/services/${id}`),
  create: (serviceData) => api.post('/services', serviceData),
  update: (id, serviceData) => api.put(`/services/${id}`, serviceData),
};

// Payments API
export const paymentsAPI = {
  getAll: () => api.get('/payments'),
  getByService: (serviceId) => api.get(`/payments/service/${serviceId}`),
  getOutstanding: () => api.get('/payments/outstanding'),
  create: (paymentData) => api.post('/payments', paymentData),
  update: (id, paymentData) => api.put(`/payments/${id}`, paymentData),
};

// Reports API
export const reportsAPI = {
  getDailyReport: (date) => api.get(`/reports/daily${date ? `?date=${date}` : ''}`),
  getMonthlyReport: (month) => api.get(`/reports/monthly${month ? `?month=${month}` : ''}`),
  getBill: (serviceId) => api.get(`/reports/bills/${serviceId}`),
};

// Generic API helper functions
export const apiHelpers = {
  // Handle API errors consistently
  handleError: (error) => {
    if (error.response) {
      // Server responded with error status
      return error.response.data?.message || 'An error occurred';
    } else if (error.request) {
      // Request was made but no response received
      return 'Network error. Please check your connection.';
    } else {
      // Something else happened
      return error.message || 'An unexpected error occurred';
    }
  },

  // Format currency for display
  formatCurrency: (amount) => {
    return new Intl.NumberFormat('en-RW', {
      style: 'currency',
      currency: 'RWF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  },

  // Format date for display
  formatDate: (dateString) => {
    return new Date(dateString).toLocaleDateString('en-RW', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  },

  // Format date for input fields
  formatDateForInput: (dateString) => {
    return new Date(dateString).toISOString().split('T')[0];
  },

  // Get today's date in YYYY-MM-DD format
  getTodayDate: () => {
    return new Date().toISOString().split('T')[0];
  },

  // Validate required fields
  validateRequired: (data, requiredFields) => {
    const errors = {};
    requiredFields.forEach(field => {
      if (!data[field] || data[field].toString().trim() === '') {
        errors[field] = `${field} is required`;
      }
    });
    return errors;
  },

  // Validate email format
  validateEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate phone number (Rwanda format)
  validatePhone: (phone) => {
    const phoneRegex = /^(\+250|0)?[7][0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  },

  // Validate plate number format
  validatePlateNumber: (plateNumber) => {
    const plateRegex = /^[A-Z]{3}-\d{3}[A-Z]$/;
    return plateRegex.test(plateNumber.toUpperCase());
  },

  // Debounce function for search inputs
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },
};

export default api;
