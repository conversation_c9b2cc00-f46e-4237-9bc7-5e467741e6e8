const express = require('express');
const { executeQuery, getOne } = require('../config/database');
const { authenticateUser } = require('../middleware/auth');
const router = express.Router();

// Apply authentication to all routes
router.use(authenticateUser);

// GET /api/reports/daily?date=YYYY-MM-DD - Daily service and payment report
router.get('/daily', async (req, res) => {
  try {
    const { date } = req.query;

    // If no date provided, use today's date
    const reportDate = date || new Date().toISOString().split('T')[0];

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(reportDate)) {
      return res.status(400).json({
        success: false,
        message: 'Date must be in YYYY-MM-DD format'
      });
    }

    // Get daily services
    const servicesQuery = `
      SELECT 
        sp.RecordNumber,
        sp.ServiceDate,
        sp.<PERSON>,
        c.<PERSON>,
        c.<PERSON>um<PERSON>,
        c.<PERSON>,
        p.PackageName,
        p.PackagePrice,
        COALESCE(pay.AmountPaid, 0) as AmountPaid,
        CASE 
          WHEN pay.AmountPaid >= p.PackagePrice THEN 'Paid'
          WHEN pay.AmountPaid > 0 THEN 'Partial'
          ELSE 'Unpaid'
        END as PaymentStatus
      FROM ServicePackage sp
      JOIN Car c ON sp.PlateNumber = c.PlateNumber
      JOIN Package p ON sp.PackageNumber = p.PackageNumber
      LEFT JOIN Payment pay ON sp.RecordNumber = pay.RecordNumber
      WHERE sp.ServiceDate = ?
      ORDER BY sp.RecordNumber
    `;

    // Get daily summary
    const summaryQuery = `
      SELECT 
        COUNT(*) as TotalServices,
        SUM(p.PackagePrice) as TotalRevenue,
        SUM(COALESCE(pay.AmountPaid, 0)) as TotalPayments,
        SUM(p.PackagePrice - COALESCE(pay.AmountPaid, 0)) as OutstandingAmount,
        AVG(p.PackagePrice) as AverageServiceValue
      FROM ServicePackage sp
      JOIN Package p ON sp.PackageNumber = p.PackageNumber
      LEFT JOIN Payment pay ON sp.RecordNumber = pay.RecordNumber
      WHERE sp.ServiceDate = ?
    `;

    // Get package breakdown
    const packageBreakdownQuery = `
      SELECT 
        p.PackageName,
        COUNT(*) as ServiceCount,
        SUM(p.PackagePrice) as TotalRevenue,
        SUM(COALESCE(pay.AmountPaid, 0)) as TotalPayments
      FROM ServicePackage sp
      JOIN Package p ON sp.PackageNumber = p.PackageNumber
      LEFT JOIN Payment pay ON sp.RecordNumber = pay.RecordNumber
      WHERE sp.ServiceDate = ?
      GROUP BY p.PackageNumber, p.PackageName
      ORDER BY ServiceCount DESC
    `;

    const [servicesResult, summaryResult, packageBreakdownResult] = await Promise.all([
      executeQuery(servicesQuery, [reportDate]),
      getOne(summaryQuery, [reportDate]),
      executeQuery(packageBreakdownQuery, [reportDate])
    ]);

    if (!servicesResult.success || !summaryResult.success || !packageBreakdownResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to generate daily report'
      });
    }

    res.json({
      success: true,
      data: {
        reportDate,
        summary: summaryResult.data || {
          TotalServices: 0,
          TotalRevenue: 0,
          TotalPayments: 0,
          OutstandingAmount: 0,
          AverageServiceValue: 0
        },
        services: servicesResult.data,
        packageBreakdown: packageBreakdownResult.data
      }
    });

  } catch (error) {
    console.error('Daily report error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/reports/bills/:serviceId - Generate detailed bill for service
router.get('/bills/:serviceId', async (req, res) => {
  try {
    const { serviceId } = req.params;

    const query = `
      SELECT 
        sp.RecordNumber,
        sp.ServiceDate,
        sp.PlateNumber,
        c.DriverName,
        c.PhoneNumber,
        c.CarType,
        c.CarSize,
        p.PackageName,
        p.PackageDescription,
        p.PackagePrice,
        COALESCE(pay.AmountPaid, 0) as AmountPaid,
        pay.PaymentDate,
        (p.PackagePrice - COALESCE(pay.AmountPaid, 0)) as Balance,
        CASE 
          WHEN pay.AmountPaid >= p.PackagePrice THEN 'Paid'
          WHEN pay.AmountPaid > 0 THEN 'Partial'
          ELSE 'Unpaid'
        END as PaymentStatus
      FROM ServicePackage sp
      JOIN Car c ON sp.PlateNumber = c.PlateNumber
      JOIN Package p ON sp.PackageNumber = p.PackageNumber
      LEFT JOIN Payment pay ON sp.RecordNumber = pay.RecordNumber
      WHERE sp.RecordNumber = ?
    `;

    const result = await getOne(query, [serviceId]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to generate bill'
      });
    }

    if (!result.data) {
      return res.status(404).json({
        success: false,
        message: 'Service record not found'
      });
    }

    // Add company information for the bill
    const billData = {
      ...result.data,
      company: {
        name: 'SmartPark Car Wash',
        location: 'Rubavu District, Rwanda',
        phone: '+*********** 000',
        email: '<EMAIL>'
      },
      billNumber: `SP-${serviceId.toString().padStart(6, '0')}`,
      generatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      data: billData
    });

  } catch (error) {
    console.error('Bill generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/reports/monthly?month=YYYY-MM - Monthly report
router.get('/monthly', async (req, res) => {
  try {
    const { month } = req.query;

    // If no month provided, use current month
    const reportMonth = month || new Date().toISOString().slice(0, 7);

    // Validate month format
    const monthRegex = /^\d{4}-\d{2}$/;
    if (!monthRegex.test(reportMonth)) {
      return res.status(400).json({
        success: false,
        message: 'Month must be in YYYY-MM format'
      });
    }

    // Get monthly summary
    const summaryQuery = `
      SELECT 
        COUNT(*) as TotalServices,
        SUM(p.PackagePrice) as TotalRevenue,
        SUM(COALESCE(pay.AmountPaid, 0)) as TotalPayments,
        SUM(p.PackagePrice - COALESCE(pay.AmountPaid, 0)) as OutstandingAmount,
        AVG(p.PackagePrice) as AverageServiceValue
      FROM ServicePackage sp
      JOIN Package p ON sp.PackageNumber = p.PackageNumber
      LEFT JOIN Payment pay ON sp.RecordNumber = pay.RecordNumber
      WHERE DATE_FORMAT(sp.ServiceDate, '%Y-%m') = ?
    `;

    // Get daily breakdown for the month
    const dailyBreakdownQuery = `
      SELECT 
        sp.ServiceDate,
        COUNT(*) as ServiceCount,
        SUM(p.PackagePrice) as DailyRevenue,
        SUM(COALESCE(pay.AmountPaid, 0)) as DailyPayments
      FROM ServicePackage sp
      JOIN Package p ON sp.PackageNumber = p.PackageNumber
      LEFT JOIN Payment pay ON sp.RecordNumber = pay.RecordNumber
      WHERE DATE_FORMAT(sp.ServiceDate, '%Y-%m') = ?
      GROUP BY sp.ServiceDate
      ORDER BY sp.ServiceDate
    `;

    const [summaryResult, dailyBreakdownResult] = await Promise.all([
      getOne(summaryQuery, [reportMonth]),
      executeQuery(dailyBreakdownQuery, [reportMonth])
    ]);

    if (!summaryResult.success || !dailyBreakdownResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to generate monthly report'
      });
    }

    res.json({
      success: true,
      data: {
        reportMonth,
        summary: summaryResult.data || {
          TotalServices: 0,
          TotalRevenue: 0,
          TotalPayments: 0,
          OutstandingAmount: 0,
          AverageServiceValue: 0
        },
        dailyBreakdown: dailyBreakdownResult.data
      }
    });

  } catch (error) {
    console.error('Monthly report error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
