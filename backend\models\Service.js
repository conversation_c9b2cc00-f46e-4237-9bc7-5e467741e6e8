const mongoose = require('mongoose');

const serviceSchema = new mongoose.Schema({
  serviceDate: {
    type: Date,
    required: true
  },
  plateNumber: {
    type: String,
    required: true,
    ref: 'Car'
  },
  packageId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Package'
  },
  packageName: {
    type: String,
    required: true
  },
  packagePrice: {
    type: Number,
    required: true,
    min: 0
  },
  carInfo: {
    driverName: {
      type: String,
      required: true
    },
    carType: {
      type: String,
      required: true
    },
    carSize: {
      type: String,
      required: true
    },
    phoneNumber: {
      type: String,
      required: true
    }
  },
  paymentStatus: {
    type: String,
    enum: ['Unpaid', 'Partial', 'Paid'],
    default: 'Unpaid'
  },
  totalPaid: {
    type: Number,
    default: 0,
    min: 0
  }
}, {
  timestamps: true
});

// Index for better search performance
serviceSchema.index({ serviceDate: 1 });
serviceSchema.index({ plateNumber: 1 });
serviceSchema.index({ packageId: 1 });
serviceSchema.index({ paymentStatus: 1 });
serviceSchema.index({ 'carInfo.driverName': 1 });

// Virtual for outstanding amount
serviceSchema.virtual('outstandingAmount').get(function() {
  return this.packagePrice - this.totalPaid;
});

// Ensure virtual fields are serialized
serviceSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Service', serviceSchema);
