# 🚀 SmartPark Car Wash Management System - Quick Start

## Step-by-Step Setup Guide

### 1. Install Prerequisites

#### Install Node.js
- Download from: https://nodejs.org/
- Install the LTS version (v18 or higher)
- Verify installation: Open Command Prompt and run `node --version`

#### Install MySQL
Choose one of these options:

**Option A: XAMPP (Recommended for beginners)**
- Download from: https://www.apachefriends.org/
- Install XAMPP
- Start Apache and MySQL from XAMPP Control Panel

**Option B: Standalone MySQL**
- Download from: https://dev.mysql.com/downloads/mysql/
- Install MySQL Server
- Remember your root password

### 2. Setup Database

#### Using XAMPP
1. Open XAMPP Control Panel
2. Start MySQL
3. Click "Admin" next to MySQL (opens phpMyAdmin)
4. Click "Import" tab
5. Choose file: `database/mysql-schema.sql`
6. Click "Go"

#### Using Command Line
```bash
# Navigate to project directory
cd "CAR WASH MANAGEMENT SYSTEM"

# Import database (enter password when prompted)
mysql -u root -p < database/mysql-schema.sql
```

### 3. Install Dependencies

#### Backend
```bash
# Open Command Prompt in project directory
cd backend
npm install
```

#### Frontend
```bash
# Open another Command Prompt
cd frontend
npm install
```

### 4. Configure Database Connection

Edit `backend/.env` file:
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=car_wash_management
DB_PORT=3306
```

### 5. Start the Application

#### Start Backend (Terminal 1)
```bash
cd backend
npm run dev
```
You should see: "🚀 Car Wash Management System API Server running on port 5000"

#### Start Frontend (Terminal 2)
```bash
cd frontend
npm start
```
Browser will open automatically at `http://localhost:3000`

### 6. Login to the System

- **URL**: http://localhost:3000
- **Username**: admin
- **Password**: admin123

## 🎯 What You'll See

### Dashboard Features
- Today's service statistics
- Quick action buttons
- Recent services and outstanding payments

### Main Modules
1. **🚗 Car Management** - Register vehicles
2. **📦 Package Management** - Service packages
3. **📝 Service Recording** - Log car wash services
4. **💳 Payment Processing** - Record payments
5. **📊 Reports** - Daily business reports
6. **🧾 Bill Generation** - Print customer bills

## 🔧 Troubleshooting

### Database Connection Issues
- Ensure MySQL is running
- Check username/password in `.env` file
- Verify database `car_wash_management` exists

### Port Already in Use
- Backend (5000): Close other applications using this port
- Frontend (3000): Close other React applications

### Missing Dependencies
```bash
# Clear cache and reinstall
npm cache clean --force
rm -rf node_modules
npm install
```

### XAMPP MySQL Won't Start
- Check if port 3306 is free
- Stop other MySQL services
- Run XAMPP as Administrator

## 📱 Using the System

### Adding a Car
1. Go to "Cars" page
2. Click "Add New Car"
3. Fill in plate number, type, size, driver info
4. Click "Add Car"

### Recording a Service
1. Go to "Services" page
2. Click "Record New Service"
3. Select date, car, and package
4. Click "Record Service"

### Processing Payment
1. Go to "Payments" page
2. Click "Record Payment"
3. Select service and enter amount
4. Click "Record Payment"

### Generating Bills
1. Go to "Bills" page
2. Find the service
3. Click "Generate Bill"
4. Print or save the bill

## 🎉 Success!

If everything is working:
- ✅ Backend running on port 5000
- ✅ Frontend running on port 3000
- ✅ Database connected
- ✅ Login successful

You now have a fully functional Car Wash Management System!

## 📞 Need Help?

Common issues and solutions:
1. **"Cannot connect to database"** → Check MySQL is running
2. **"Port 3000 already in use"** → Close other React apps
3. **"Module not found"** → Run `npm install` in the correct directory
4. **"Access denied for user"** → Check MySQL credentials

---

**SmartPark Car Wash Management System** - Making car wash business management simple and efficient!
