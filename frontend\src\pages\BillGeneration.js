import React, { useState, useEffect } from 'react';
import { Search, Receipt, Printer, Eye } from 'lucide-react';
import { servicesAPI, reportsAPI, apiHelpers } from '../services/api';
import toast from 'react-hot-toast';

const BillGeneration = () => {
  const [services, setServices] = useState([]);
  const [filteredServices, setFilteredServices] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBill, setSelectedBill] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showBillModal, setShowBillModal] = useState(false);

  useEffect(() => {
    fetchServices();
  }, []);

  useEffect(() => {
    filterServices();
  }, [services, searchTerm]);

  const fetchServices = async () => {
    try {
      setIsLoading(true);
      const response = await servicesAPI.getAll();
      setServices(response.data.data);
    } catch (error) {
      toast.error('Failed to load services');
      console.error('Fetch services error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterServices = () => {
    if (!searchTerm) {
      setFilteredServices(services);
      return;
    }

    const filtered = services.filter(service =>
      service.plateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.carInfo?.driverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.packageName.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredServices(filtered);
  };

  const generateBill = async (serviceId) => {
    try {
      const response = await reportsAPI.getBill(serviceId);
      setSelectedBill(response.data.data);
      setShowBillModal(true);
    } catch (error) {
      toast.error('Failed to generate bill');
      console.error('Generate bill error:', error);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-secondary-200 rounded w-1/4 mb-6"></div>
          <div className="card">
            <div className="card-body">
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-12 bg-secondary-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Bill Generation</h1>
          <p className="text-secondary-600">Generate and print bills for car wash services</p>
        </div>
      </div>

      {/* Search */}
      <div className="card">
        <div className="card-body">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-400" />
            <input
              type="text"
              placeholder="Search by plate number, driver name, or package..."
              className="input pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Services Table */}
      <div className="card">
        <div className="card-body p-0">
          {filteredServices.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="table">
                <thead>
                  <tr>
                    <th>Service Date</th>
                    <th>Plate Number</th>
                    <th>Driver</th>
                    <th>Package</th>
                    <th>Amount</th>
                    <th>Payment Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredServices.map((service) => (
                    <tr key={service._id}>
                      <td>{apiHelpers.formatDate(service.serviceDate)}</td>
                      <td className="font-medium">{service.plateNumber}</td>
                      <td>{service.carInfo?.driverName}</td>
                      <td>{service.packageName}</td>
                      <td className="font-medium">
                        {apiHelpers.formatCurrency(service.packagePrice)}
                      </td>
                      <td>
                        <span className={`badge ${
                          service.paymentStatus === 'Paid' ? 'badge-success' :
                          service.paymentStatus === 'Partial' ? 'badge-warning' :
                          'badge-danger'
                        }`}>
                          {service.paymentStatus}
                        </span>
                      </td>
                      <td>
                        <button
                          onClick={() => generateBill(service._id)}
                          className="btn-primary btn-sm"
                          title="Generate Bill"
                        >
                          <Receipt className="h-4 w-4 mr-1" />
                          Generate Bill
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <Receipt className="h-12 w-12 text-secondary-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-secondary-900 mb-2">No services found</h3>
              <p className="text-secondary-500">
                {searchTerm ? 'No services match your search criteria.' : 'No services available for bill generation.'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Bill Modal */}
      {showBillModal && selectedBill && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6 no-print">
                <h2 className="text-xl font-bold text-secondary-900">Service Bill</h2>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handlePrint}
                    className="btn-primary"
                  >
                    <Printer className="h-4 w-4 mr-2" />
                    Print
                  </button>
                  <button
                    onClick={() => setShowBillModal(false)}
                    className="btn-outline"
                  >
                    Close
                  </button>
                </div>
              </div>

              {/* Bill Content */}
              <div className="space-y-6">
                {/* Company Header */}
                <div className="text-center border-b pb-4">
                  <h1 className="text-2xl font-bold text-primary-600">SmartPark Car Wash</h1>
                  <p className="text-secondary-600">Rubavu District, Rwanda</p>
                  <p className="text-secondary-600">Phone: +*********** 000 | Email: <EMAIL></p>
                </div>

                {/* Bill Details */}
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold text-secondary-900 mb-2">Bill To:</h3>
                    <p className="text-secondary-700">{selectedBill.carInfo?.driverName}</p>
                    <p className="text-secondary-600">{selectedBill.carInfo?.phoneNumber}</p>
                    <p className="text-secondary-600">Vehicle: {selectedBill.plateNumber}</p>
                  </div>
                  <div className="text-right">
                    <h3 className="font-semibold text-secondary-900 mb-2">Bill Details:</h3>
                    <p className="text-secondary-700">Bill #: {selectedBill.billNumber}</p>
                    <p className="text-secondary-600">Service Date: {apiHelpers.formatDate(selectedBill.serviceDate)}</p>
                    <p className="text-secondary-600">Generated: {apiHelpers.formatDate(new Date())}</p>
                  </div>
                </div>

                {/* Service Details */}
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-secondary-50">
                      <tr>
                        <th className="px-4 py-3 text-left font-medium text-secondary-900">Service</th>
                        <th className="px-4 py-3 text-left font-medium text-secondary-900">Description</th>
                        <th className="px-4 py-3 text-right font-medium text-secondary-900">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-t">
                        <td className="px-4 py-3 font-medium">{selectedBill.packageName}</td>
                        <td className="px-4 py-3 text-secondary-600">
                          {selectedBill.packageDescription || 'Car wash service'}
                        </td>
                        <td className="px-4 py-3 text-right font-medium">
                          {apiHelpers.formatCurrency(selectedBill.packagePrice)}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                {/* Payment Summary */}
                <div className="bg-secondary-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">Total Amount:</span>
                    <span className="font-bold text-lg">
                      {apiHelpers.formatCurrency(selectedBill.packagePrice)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center mb-2">
                    <span>Amount Paid:</span>
                    <span className="text-green-600">
                      {apiHelpers.formatCurrency(selectedBill.totalPaid || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center border-t pt-2">
                    <span className="font-medium">Balance Due:</span>
                    <span className={`font-bold ${selectedBill.outstandingAmount > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                      {apiHelpers.formatCurrency(selectedBill.outstandingAmount || 0)}
                    </span>
                  </div>
                </div>

                {/* Payment Status */}
                <div className="text-center">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    selectedBill.paymentStatus === 'Paid' ? 'bg-green-100 text-green-800' :
                    selectedBill.paymentStatus === 'Partial' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    Payment Status: {selectedBill.paymentStatus}
                  </span>
                </div>

                {/* Footer */}
                <div className="text-center text-sm text-secondary-500 border-t pt-4">
                  <p>Thank you for choosing SmartPark Car Wash!</p>
                  <p>For any inquiries, please contact us at +*********** 000</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BillGeneration;
