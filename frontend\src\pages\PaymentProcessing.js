import React, { useState, useEffect } from 'react';
import { Plus, CreditCard, Calendar, AlertCircle } from 'lucide-react';
import { paymentsAPI, servicesAPI, apiHelpers } from '../services/api';
import toast from 'react-hot-toast';

const PaymentProcessing = () => {
  const [payments, setPayments] = useState([]);
  const [outstandingServices, setOutstandingServices] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState({
    serviceId: '',
    amountPaid: '',
    paymentDate: apiHelpers.getTodayDate(),
    paymentMethod: 'Cash'
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const [paymentsResponse, outstandingResponse] = await Promise.all([
        paymentsAPI.getAll(),
        paymentsAPI.getOutstanding()
      ]);
      
      setPayments(paymentsResponse.data.data);
      setOutstandingServices(outstandingResponse.data.data);
    } catch (error) {
      toast.error('Failed to load payment data');
      console.error('Fetch payments error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      await paymentsAPI.create({
        ...formData,
        amountPaid: parseFloat(formData.amountPaid)
      });
      toast.success('Payment recorded successfully');
      fetchData();
      setShowModal(false);
      setFormData({
        serviceId: '',
        amountPaid: '',
        paymentDate: apiHelpers.getTodayDate(),
        paymentMethod: 'Cash'
      });
    } catch (error) {
      toast.error('Failed to record payment');
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-secondary-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="card">
                <div className="card-body">
                  <div className="space-y-3">
                    {[...Array(3)].map((_, j) => (
                      <div key={j} className="h-12 bg-secondary-200 rounded"></div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Payment Processing</h1>
          <p className="text-secondary-600">Record payments and track outstanding amounts</p>
        </div>
        <button
          onClick={() => setShowModal(true)}
          className="btn-primary"
        >
          <Plus className="h-4 w-4 mr-2" />
          Record Payment
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Outstanding Payments */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-secondary-900 flex items-center">
              <AlertCircle className="h-5 w-5 text-orange-500 mr-2" />
              Outstanding Payments
            </h2>
          </div>
          <div className="card-body">
            {outstandingServices.length > 0 ? (
              <div className="space-y-3">
                {outstandingServices.slice(0, 5).map((service) => (
                  <div key={service._id} className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium text-secondary-900">
                          {service.plateNumber}
                        </p>
                        <p className="text-sm text-secondary-600">
                          {service.carInfo?.driverName}
                        </p>
                        <p className="text-xs text-secondary-500">
                          {apiHelpers.formatDate(service.serviceDate)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-orange-600">
                          {apiHelpers.formatCurrency(service.outstandingAmount)}
                        </p>
                        <p className="text-xs text-secondary-500">
                          of {apiHelpers.formatCurrency(service.packagePrice)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-secondary-300 mx-auto mb-3" />
                <p className="text-secondary-500">No outstanding payments</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Payments */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-secondary-900">Recent Payments</h2>
          </div>
          <div className="card-body">
            {payments.length > 0 ? (
              <div className="space-y-3">
                {payments.slice(0, 5).map((payment) => (
                  <div key={payment._id} className="p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium text-secondary-900">
                          {payment.plateNumber}
                        </p>
                        <p className="text-sm text-secondary-600">
                          {payment.packageName}
                        </p>
                        <p className="text-xs text-secondary-500">
                          {apiHelpers.formatDate(payment.paymentDate)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-green-600">
                          {apiHelpers.formatCurrency(payment.amountPaid)}
                        </p>
                        <p className="text-xs text-secondary-500">
                          {payment.paymentMethod}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-secondary-300 mx-auto mb-3" />
                <p className="text-secondary-500">No payments recorded</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6">
              <h2 className="text-xl font-bold text-secondary-900 mb-6">
                Record Payment
              </h2>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="label">Service</label>
                  <select
                    className="input"
                    value={formData.serviceId}
                    onChange={(e) => setFormData({...formData, serviceId: e.target.value})}
                    required
                  >
                    <option value="">Select a service</option>
                    {outstandingServices.map(service => (
                      <option key={service._id} value={service._id}>
                        {service.plateNumber} - {service.packageName} - {apiHelpers.formatCurrency(service.outstandingAmount)}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="label">Amount Paid (RWF)</label>
                  <input
                    type="number"
                    className="input"
                    placeholder="0"
                    min="0"
                    step="100"
                    value={formData.amountPaid}
                    onChange={(e) => setFormData({...formData, amountPaid: e.target.value})}
                    required
                  />
                </div>

                <div>
                  <label className="label">Payment Date</label>
                  <input
                    type="date"
                    className="input"
                    value={formData.paymentDate}
                    onChange={(e) => setFormData({...formData, paymentDate: e.target.value})}
                    required
                  />
                </div>

                <div>
                  <label className="label">Payment Method</label>
                  <select
                    className="input"
                    value={formData.paymentMethod}
                    onChange={(e) => setFormData({...formData, paymentMethod: e.target.value})}
                  >
                    <option value="Cash">Cash</option>
                    <option value="Mobile Money">Mobile Money</option>
                    <option value="Bank Transfer">Bank Transfer</option>
                    <option value="Card">Card</option>
                  </select>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="flex-1 btn-outline"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="flex-1 btn-primary"
                  >
                    Record Payment
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentProcessing;
