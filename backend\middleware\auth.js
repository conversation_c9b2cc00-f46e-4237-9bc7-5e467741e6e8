// Authentication middleware for protecting routes
const authenticateUser = (req, res, next) => {
  // Check if user is logged in via session
  if (req.session && req.session.user) {
    // User is authenticated, proceed to next middleware
    req.user = req.session.user;
    return next();
  } else {
    // User is not authenticated
    return res.status(401).json({
      success: false,
      message: 'Authentication required. Please log in.'
    });
  }
};

// Optional authentication - doesn't block if not authenticated
const optionalAuth = (req, res, next) => {
  if (req.session && req.session.user) {
    req.user = req.session.user;
  }
  next();
};

// Check if user is admin (for package management)
const requireAdmin = (req, res, next) => {
  if (!req.session || !req.session.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required.'
    });
  }

  // For this system, we'll consider 'admin' username as admin
  if (req.session.user.username === 'admin') {
    return next();
  } else {
    return res.status(403).json({
      success: false,
      message: 'Admin privileges required for this action.'
    });
  }
};

// Validate session and refresh if needed
const validateSession = (req, res, next) => {
  if (req.session && req.session.user) {
    // Refresh session timeout
    req.session.touch();
  }
  next();
};

module.exports = {
  authenticateUser,
  optionalAuth,
  requireAdmin,
  validateSession
};
