import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, Car, Phone, User } from 'lucide-react';
import { carsAPI, apiHelpers } from '../services/api';
import toast from 'react-hot-toast';

const CarManagement = () => {
  const [cars, setCars] = useState([]);
  const [filteredCars, setFilteredCars] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingCar, setEditingCar] = useState(null);
  const [formData, setFormData] = useState({
    plateNumber: '',
    carType: '',
    carSize: '',
    driverName: '',
    phoneNumber: ''
  });
  const [errors, setErrors] = useState({});

  const carTypes = ['Sedan', 'SUV', 'Hatchback', 'Truck', 'Van', 'Coupe', 'Convertible'];
  const carSizes = ['Small', 'Medium', 'Large'];

  useEffect(() => {
    fetchCars();
  }, []);

  useEffect(() => {
    filterCars();
  }, [cars, searchTerm]);

  const fetchCars = async () => {
    try {
      setIsLoading(true);
      const response = await carsAPI.getAll();
      setCars(response.data.data);
    } catch (error) {
      toast.error('Failed to load cars');
      console.error('Fetch cars error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterCars = () => {
    if (!searchTerm) {
      setFilteredCars(cars);
      return;
    }

    const filtered = cars.filter(car =>
      car.PlateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      car.DriverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      car.CarType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      car.PhoneNumber.includes(searchTerm)
    );
    setFilteredCars(filtered);
  };

  const resetForm = () => {
    setFormData({
      plateNumber: '',
      carType: '',
      carSize: '',
      driverName: '',
      phoneNumber: ''
    });
    setErrors({});
    setEditingCar(null);
  };

  const openModal = (car = null) => {
    if (car) {
      setEditingCar(car);
      setFormData({
        plateNumber: car.PlateNumber,
        carType: car.CarType,
        carSize: car.CarSize,
        driverName: car.DriverName,
        phoneNumber: car.PhoneNumber
      });
    } else {
      resetForm();
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    resetForm();
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = apiHelpers.validateRequired(formData, [
      'plateNumber', 'carType', 'carSize', 'driverName', 'phoneNumber'
    ]);

    // Validate plate number format
    if (formData.plateNumber && !apiHelpers.validatePlateNumber(formData.plateNumber)) {
      newErrors.plateNumber = 'Plate number must be in format: ABC-123D';
    }

    // Validate phone number
    if (formData.phoneNumber && !apiHelpers.validatePhone(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid Rwanda phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      if (editingCar) {
        await carsAPI.update(editingCar.PlateNumber, {
          carType: formData.carType,
          carSize: formData.carSize,
          driverName: formData.driverName,
          phoneNumber: formData.phoneNumber
        });
        toast.success('Car updated successfully');
      } else {
        await carsAPI.create(formData);
        toast.success('Car added successfully');
      }
      
      fetchCars();
      closeModal();
    } catch (error) {
      const errorMessage = apiHelpers.handleError(error);
      toast.error(errorMessage);
    }
  };

  const handleDelete = async (plateNumber) => {
    if (!window.confirm('Are you sure you want to delete this car? This will also delete all associated service records.')) {
      return;
    }

    try {
      await carsAPI.delete(plateNumber);
      toast.success('Car deleted successfully');
      fetchCars();
    } catch (error) {
      const errorMessage = apiHelpers.handleError(error);
      toast.error(errorMessage);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-secondary-200 rounded w-1/4 mb-6"></div>
          <div className="card">
            <div className="card-body">
              <div className="h-4 bg-secondary-200 rounded w-full mb-4"></div>
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-12 bg-secondary-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Car Management</h1>
          <p className="text-secondary-600">Manage registered vehicles and their information</p>
        </div>
        <button
          onClick={() => openModal()}
          className="btn-primary"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New Car
        </button>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="card-body">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-400" />
              <input
                type="text"
                placeholder="Search by plate number, driver name, car type, or phone..."
                className="input pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="text-sm text-secondary-600">
              {filteredCars.length} of {cars.length} cars
            </div>
          </div>
        </div>
      </div>

      {/* Cars Table */}
      <div className="card">
        <div className="card-body p-0">
          {filteredCars.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="table">
                <thead>
                  <tr>
                    <th>Plate Number</th>
                    <th>Driver Name</th>
                    <th>Car Type</th>
                    <th>Size</th>
                    <th>Phone Number</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCars.map((car) => (
                    <tr key={car.PlateNumber}>
                      <td>
                        <div className="flex items-center space-x-2">
                          <Car className="h-4 w-4 text-secondary-400" />
                          <span className="font-medium">{car.PlateNumber}</span>
                        </div>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-secondary-400" />
                          <span>{car.DriverName}</span>
                        </div>
                      </td>
                      <td>
                        <span className="badge badge-secondary">{car.CarType}</span>
                      </td>
                      <td>
                        <span className={`badge ${
                          car.CarSize === 'Small' ? 'badge-success' :
                          car.CarSize === 'Medium' ? 'badge-warning' :
                          'badge-danger'
                        }`}>
                          {car.CarSize}
                        </span>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4 text-secondary-400" />
                          <span>{car.PhoneNumber}</span>
                        </div>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => openModal(car)}
                            className="p-2 text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                            title="Edit car"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(car.PlateNumber)}
                            className="p-2 text-danger-600 hover:bg-danger-50 rounded-lg transition-colors"
                            title="Delete car"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <Car className="h-12 w-12 text-secondary-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-secondary-900 mb-2">No cars found</h3>
              <p className="text-secondary-500 mb-6">
                {searchTerm ? 'No cars match your search criteria.' : 'Get started by adding your first car.'}
              </p>
              {!searchTerm && (
                <button
                  onClick={() => openModal()}
                  className="btn-primary"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Car
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h2 className="text-xl font-bold text-secondary-900 mb-6">
                {editingCar ? 'Edit Car' : 'Add New Car'}
              </h2>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="label">Plate Number</label>
                  <input
                    type="text"
                    name="plateNumber"
                    className={`input ${errors.plateNumber ? 'border-danger-300' : ''}`}
                    placeholder="e.g., RAB-001A"
                    value={formData.plateNumber}
                    onChange={handleInputChange}
                    disabled={editingCar}
                  />
                  {errors.plateNumber && (
                    <p className="mt-1 text-sm text-danger-600">{errors.plateNumber}</p>
                  )}
                </div>

                <div>
                  <label className="label">Car Type</label>
                  <select
                    name="carType"
                    className={`input ${errors.carType ? 'border-danger-300' : ''}`}
                    value={formData.carType}
                    onChange={handleInputChange}
                  >
                    <option value="">Select car type</option>
                    {carTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                  {errors.carType && (
                    <p className="mt-1 text-sm text-danger-600">{errors.carType}</p>
                  )}
                </div>

                <div>
                  <label className="label">Car Size</label>
                  <select
                    name="carSize"
                    className={`input ${errors.carSize ? 'border-danger-300' : ''}`}
                    value={formData.carSize}
                    onChange={handleInputChange}
                  >
                    <option value="">Select car size</option>
                    {carSizes.map(size => (
                      <option key={size} value={size}>{size}</option>
                    ))}
                  </select>
                  {errors.carSize && (
                    <p className="mt-1 text-sm text-danger-600">{errors.carSize}</p>
                  )}
                </div>

                <div>
                  <label className="label">Driver Name</label>
                  <input
                    type="text"
                    name="driverName"
                    className={`input ${errors.driverName ? 'border-danger-300' : ''}`}
                    placeholder="Enter driver's full name"
                    value={formData.driverName}
                    onChange={handleInputChange}
                  />
                  {errors.driverName && (
                    <p className="mt-1 text-sm text-danger-600">{errors.driverName}</p>
                  )}
                </div>

                <div>
                  <label className="label">Phone Number</label>
                  <input
                    type="tel"
                    name="phoneNumber"
                    className={`input ${errors.phoneNumber ? 'border-danger-300' : ''}`}
                    placeholder="+250 788 123 456"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                  />
                  {errors.phoneNumber && (
                    <p className="mt-1 text-sm text-danger-600">{errors.phoneNumber}</p>
                  )}
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="flex-1 btn-outline"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="flex-1 btn-primary"
                  >
                    {editingCar ? 'Update Car' : 'Add Car'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CarManagement;
