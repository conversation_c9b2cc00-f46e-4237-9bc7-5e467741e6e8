-- Car Washing Sales Management System Database Schema
-- SmartPark Company - Rubavu District, Rwanda

-- Create database
CREATE DATABASE IF NOT EXISTS car_wash_management;
USE car_wash_management;

-- 1. Package Table
CREATE TABLE Package (
    PackageNumber INT PRIMARY KEY AUTO_INCREMENT,
    PackageName VARCHAR(100) NOT NULL,
    PackageDescription TEXT,
    PackagePrice DECIMAL(10,2) NOT NULL
);

-- 2. Car Table
CREATE TABLE Car (
    PlateNumber VARCHAR(20) PRIMARY KEY,
    CarType VARCHAR(50) NOT NULL,
    CarSize VARCHAR(20) NOT NULL,
    DriverName VARCHAR(100) NOT NULL,
    PhoneNumber VARCHAR(15) NOT NULL
);

-- 3. ServicePackage Table
CREATE TABLE ServicePackage (
    RecordNumber INT PRIMARY KEY AUTO_INCREMENT,
    ServiceDate DATE NOT NULL,
    PlateNumber VARCHAR(20) NOT NULL,
    PackageNumber INT NOT NULL,
    FOREIGN KEY (PlateNumber) REFERENCES Car(PlateNumber) ON DELETE CASCADE,
    FOREIGN KEY (PackageNumber) REFERENCES Package(PackageNumber) ON DELETE CASCADE
);

-- 4. Payment Table
CREATE TABLE Payment (
    PaymentNumber INT PRIMARY KEY AUTO_INCREMENT,
    AmountPaid DECIMAL(10,2) NOT NULL,
    PaymentDate DATE NOT NULL,
    RecordNumber INT NOT NULL,
    FOREIGN KEY (RecordNumber) REFERENCES ServicePackage(RecordNumber) ON DELETE CASCADE
);

-- 5. User Table
CREATE TABLE User (
    UserID INT PRIMARY KEY AUTO_INCREMENT,
    Username VARCHAR(50) UNIQUE NOT NULL,
    Password VARCHAR(255) NOT NULL,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample package data
INSERT INTO Package (PackageName, PackageDescription, PackagePrice) VALUES
('Basic Wash', 'Exterior hand wash only - Quick and efficient cleaning', 5000.00),
('Premium Wash', 'Exterior + interior cleaning - Complete car care', 8000.00),
('Deluxe Wash', 'Full service with wax - Premium treatment with protection', 12000.00),
('Express Wash', 'Quick exterior wash for busy customers', 3500.00),
('VIP Service', 'Complete detailing with premium products', 15000.00);

-- Insert sample user data (password: admin123 - will be hashed in application)
INSERT INTO User (Username, Password) VALUES
('admin', 'admin123'),
('manager', 'admin123');

-- Insert sample car data
INSERT INTO Car (PlateNumber, CarType, CarSize, DriverName, PhoneNumber) VALUES
('RAB-001A', 'Sedan', 'Medium', 'Jean Baptiste', '+250788123456'),
('RAB-002B', 'SUV', 'Large', 'Marie Claire', '+250788234567'),
('RAB-003C', 'Truck', 'Large', 'Paul Kagame', '+250788345678'),
('RAB-004D', 'Sedan', 'Small', 'Grace Uwimana', '+250788456789'),
('RAB-005E', 'Hatchback', 'Small', 'Eric Nzeyimana', '+250788567890');

-- Insert sample service records
INSERT INTO ServicePackage (ServiceDate, PlateNumber, PackageNumber) VALUES
('2025-01-15', 'RAB-001A', 1),
('2025-01-15', 'RAB-002B', 2),
('2025-01-16', 'RAB-003C', 3),
('2025-01-16', 'RAB-004D', 1),
('2025-01-17', 'RAB-005E', 2);

-- Insert sample payment records
INSERT INTO Payment (AmountPaid, PaymentDate, RecordNumber) VALUES
(5000.00, '2025-01-15', 1),
(8000.00, '2025-01-15', 2),
(12000.00, '2025-01-16', 3),
(5000.00, '2025-01-16', 4),
(8000.00, '2025-01-17', 5);

-- Create indexes for better performance
CREATE INDEX idx_service_date ON ServicePackage(ServiceDate);
CREATE INDEX idx_payment_date ON Payment(PaymentDate);
CREATE INDEX idx_car_type ON Car(CarType);
CREATE INDEX idx_car_size ON Car(CarSize);

-- Create views for reporting
CREATE VIEW DailyServiceReport AS
SELECT 
    sp.ServiceDate,
    COUNT(*) as TotalServices,
    SUM(p.PackagePrice) as TotalRevenue,
    AVG(p.PackagePrice) as AverageServiceValue
FROM ServicePackage sp
JOIN Package p ON sp.PackageNumber = p.PackageNumber
GROUP BY sp.ServiceDate
ORDER BY sp.ServiceDate DESC;

CREATE VIEW ServiceWithPaymentStatus AS
SELECT 
    sp.RecordNumber,
    sp.ServiceDate,
    c.PlateNumber,
    c.DriverName,
    c.PhoneNumber,
    pkg.PackageName,
    pkg.PackagePrice,
    COALESCE(pay.AmountPaid, 0) as AmountPaid,
    CASE 
        WHEN pay.AmountPaid >= pkg.PackagePrice THEN 'Paid'
        WHEN pay.AmountPaid > 0 THEN 'Partial'
        ELSE 'Unpaid'
    END as PaymentStatus
FROM ServicePackage sp
JOIN Car c ON sp.PlateNumber = c.PlateNumber
JOIN Package pkg ON sp.PackageNumber = pkg.PackageNumber
LEFT JOIN Payment pay ON sp.RecordNumber = pay.RecordNumber;
