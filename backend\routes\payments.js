const express = require('express');
const { executeQuery, getOne } = require('../config/database');
const { authenticateUser } = require('../middleware/auth');
const router = express.Router();

// Apply authentication to all routes
router.use(authenticateUser);

// GET /api/payments - Retrieve all payments
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT 
        pay.PaymentNumber,
        pay.AmountPaid,
        pay.PaymentDate,
        pay.RecordNumber,
        sp.ServiceDate,
        sp.PlateNumber,
        c.DriverName,
        p.PackageName,
        p.PackagePrice,
        (p.PackagePrice - pay.AmountPaid) as Balance
      FROM Payment pay
      JOIN ServicePackage sp ON pay.RecordNumber = sp.RecordNumber
      JOIN Car c ON sp.PlateNumber = c.PlateNumber
      JOIN Package p ON sp.PackageNumber = p.PackageNumber
      ORDER BY pay.PaymentDate DESC, pay.PaymentNumber DESC
    `;
    
    const result = await executeQuery(query);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve payments'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get payments error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/payments/service/:serviceId - Get payments for specific service
router.get('/service/:serviceId', async (req, res) => {
  try {
    const { serviceId } = req.params;
    
    const query = `
      SELECT 
        pay.PaymentNumber,
        pay.AmountPaid,
        pay.PaymentDate,
        pay.RecordNumber
      FROM Payment pay
      WHERE pay.RecordNumber = ?
      ORDER BY pay.PaymentDate DESC
    `;
    
    const result = await executeQuery(query, [serviceId]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve payments'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get service payments error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/payments/outstanding - Get services with outstanding payments
router.get('/outstanding', async (req, res) => {
  try {
    const query = `
      SELECT 
        sp.RecordNumber,
        sp.ServiceDate,
        sp.PlateNumber,
        c.DriverName,
        c.PhoneNumber,
        p.PackageName,
        p.PackagePrice,
        COALESCE(pay.AmountPaid, 0) as AmountPaid,
        (p.PackagePrice - COALESCE(pay.AmountPaid, 0)) as OutstandingAmount
      FROM ServicePackage sp
      JOIN Car c ON sp.PlateNumber = c.PlateNumber
      JOIN Package p ON sp.PackageNumber = p.PackageNumber
      LEFT JOIN Payment pay ON sp.RecordNumber = pay.RecordNumber
      WHERE COALESCE(pay.AmountPaid, 0) < p.PackagePrice
      ORDER BY sp.ServiceDate DESC
    `;
    
    const result = await executeQuery(query);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve outstanding payments'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get outstanding payments error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// POST /api/payments - Record new payment
router.post('/', async (req, res) => {
  try {
    const { amountPaid, paymentDate, recordNumber } = req.body;

    // Validate required fields
    if (!amountPaid || !paymentDate || !recordNumber) {
      return res.status(400).json({
        success: false,
        message: 'Amount paid, payment date, and record number are required'
      });
    }

    // Validate amount is a positive number
    if (isNaN(amountPaid) || amountPaid <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Amount paid must be a positive number'
      });
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(paymentDate)) {
      return res.status(400).json({
        success: false,
        message: 'Payment date must be in YYYY-MM-DD format'
      });
    }

    // Check if service record exists
    const serviceExists = await getOne('SELECT RecordNumber FROM ServicePackage WHERE RecordNumber = ?', [recordNumber]);
    if (!serviceExists.data) {
      return res.status(404).json({
        success: false,
        message: 'Service record not found'
      });
    }

    // Check if payment already exists for this service
    const existingPayment = await getOne('SELECT PaymentNumber FROM Payment WHERE RecordNumber = ?', [recordNumber]);
    if (existingPayment.data) {
      return res.status(409).json({
        success: false,
        message: 'Payment already exists for this service. Use update instead.'
      });
    }

    // Get package price to validate payment amount
    const packageInfo = await getOne(`
      SELECT p.PackagePrice 
      FROM ServicePackage sp 
      JOIN Package p ON sp.PackageNumber = p.PackageNumber 
      WHERE sp.RecordNumber = ?
    `, [recordNumber]);

    if (amountPaid > packageInfo.data.PackagePrice) {
      return res.status(400).json({
        success: false,
        message: `Payment amount cannot exceed package price of ${packageInfo.data.PackagePrice} RWF`
      });
    }

    const query = `
      INSERT INTO Payment (AmountPaid, PaymentDate, RecordNumber) 
      VALUES (?, ?, ?)
    `;
    const result = await executeQuery(query, [amountPaid, paymentDate, recordNumber]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to record payment'
      });
    }

    res.status(201).json({
      success: true,
      message: 'Payment recorded successfully',
      data: { 
        paymentNumber: result.data.insertId,
        amountPaid, 
        paymentDate, 
        recordNumber 
      }
    });

  } catch (error) {
    console.error('Add payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// PUT /api/payments/:id - Update payment
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { amountPaid, paymentDate } = req.body;

    // Validate required fields
    if (!amountPaid || !paymentDate) {
      return res.status(400).json({
        success: false,
        message: 'Amount paid and payment date are required'
      });
    }

    // Validate amount is a positive number
    if (isNaN(amountPaid) || amountPaid <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Amount paid must be a positive number'
      });
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(paymentDate)) {
      return res.status(400).json({
        success: false,
        message: 'Payment date must be in YYYY-MM-DD format'
      });
    }

    // Check if payment exists
    const paymentExists = await getOne('SELECT PaymentNumber, RecordNumber FROM Payment WHERE PaymentNumber = ?', [id]);
    if (!paymentExists.data) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Get package price to validate payment amount
    const packageInfo = await getOne(`
      SELECT p.PackagePrice 
      FROM ServicePackage sp 
      JOIN Package p ON sp.PackageNumber = p.PackageNumber 
      WHERE sp.RecordNumber = ?
    `, [paymentExists.data.RecordNumber]);

    if (amountPaid > packageInfo.data.PackagePrice) {
      return res.status(400).json({
        success: false,
        message: `Payment amount cannot exceed package price of ${packageInfo.data.PackagePrice} RWF`
      });
    }

    const query = `
      UPDATE Payment 
      SET AmountPaid = ?, PaymentDate = ? 
      WHERE PaymentNumber = ?
    `;
    const result = await executeQuery(query, [amountPaid, paymentDate, id]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to update payment'
      });
    }

    res.json({
      success: true,
      message: 'Payment updated successfully'
    });

  } catch (error) {
    console.error('Update payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
