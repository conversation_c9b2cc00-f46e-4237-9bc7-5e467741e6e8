import React, { useState, useEffect } from 'react';
import { Calendar, BarChart3, Download, TrendingUp } from 'lucide-react';
import { reportsAPI, apiHelpers } from '../services/api';
import toast from 'react-hot-toast';

const Reports = () => {
  const [reportData, setReportData] = useState(null);
  const [selectedDate, setSelectedDate] = useState(apiHelpers.getTodayDate());
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchDailyReport();
  }, [selectedDate]);

  const fetchDailyReport = async () => {
    try {
      setIsLoading(true);
      const response = await reportsAPI.getDailyReport(selectedDate);
      setReportData(response.data.data);
    } catch (error) {
      toast.error('Failed to load report data');
      console.error('Fetch report error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-secondary-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card">
                <div className="card-body">
                  <div className="h-4 bg-secondary-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-secondary-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Reports</h1>
          <p className="text-secondary-600">View daily and monthly business reports</p>
        </div>
        <div className="flex items-center space-x-3">
          <input
            type="date"
            className="input"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
          />
          <button
            onClick={handlePrint}
            className="btn-outline no-print"
          >
            <Download className="h-4 w-4 mr-2" />
            Print Report
          </button>
        </div>
      </div>

      {reportData && (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-secondary-600">Total Services</p>
                    <p className="text-2xl font-bold text-secondary-900">
                      {reportData.summary.TotalServices || 0}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-blue-100">
                    <BarChart3 className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-secondary-600">Total Revenue</p>
                    <p className="text-2xl font-bold text-secondary-900">
                      {apiHelpers.formatCurrency(reportData.summary.TotalRevenue || 0)}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-green-100">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-secondary-600">Payments Received</p>
                    <p className="text-2xl font-bold text-secondary-900">
                      {apiHelpers.formatCurrency(reportData.summary.TotalPayments || 0)}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-purple-100">
                    <Calendar className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-secondary-600">Outstanding</p>
                    <p className="text-2xl font-bold text-secondary-900">
                      {apiHelpers.formatCurrency(reportData.summary.OutstandingAmount || 0)}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-orange-100">
                    <Calendar className="h-6 w-6 text-orange-600" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Package Breakdown */}
          {reportData.packageBreakdown && reportData.packageBreakdown.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-semibold text-secondary-900">Package Breakdown</h2>
              </div>
              <div className="card-body p-0">
                <div className="overflow-x-auto">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>Package Name</th>
                        <th>Services Count</th>
                        <th>Total Revenue</th>
                        <th>Total Payments</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.packageBreakdown.map((pkg, index) => (
                        <tr key={index}>
                          <td className="font-medium">{pkg.PackageName}</td>
                          <td>{pkg.ServiceCount}</td>
                          <td>{apiHelpers.formatCurrency(pkg.TotalRevenue)}</td>
                          <td>{apiHelpers.formatCurrency(pkg.TotalPayments)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Services List */}
          {reportData.services && reportData.services.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-semibold text-secondary-900">
                  Services for {apiHelpers.formatDate(selectedDate)}
                </h2>
              </div>
              <div className="card-body p-0">
                <div className="overflow-x-auto">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>Plate Number</th>
                        <th>Driver</th>
                        <th>Package</th>
                        <th>Price</th>
                        <th>Payment Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.services.map((service) => (
                        <tr key={service._id}>
                          <td className="font-medium">{service.plateNumber}</td>
                          <td>{service.carInfo?.driverName}</td>
                          <td>{service.packageName}</td>
                          <td>{apiHelpers.formatCurrency(service.packagePrice)}</td>
                          <td>
                            <span className={`badge ${
                              service.paymentStatus === 'Paid' ? 'badge-success' :
                              service.paymentStatus === 'Partial' ? 'badge-warning' :
                              'badge-danger'
                            }`}>
                              {service.paymentStatus}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* No Data Message */}
          {(!reportData.services || reportData.services.length === 0) && (
            <div className="card">
              <div className="card-body text-center py-12">
                <BarChart3 className="h-12 w-12 text-secondary-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-secondary-900 mb-2">No services found</h3>
                <p className="text-secondary-500">
                  No services were recorded for {apiHelpers.formatDate(selectedDate)}.
                </p>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default Reports;
