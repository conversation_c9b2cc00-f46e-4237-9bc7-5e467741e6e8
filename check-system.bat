@echo off
echo ========================================
echo SmartPark System Check
echo ========================================
echo.

echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js not found! Please install Node.js from https://nodejs.org/
) else (
    echo ✅ Node.js is installed
)

echo.
echo Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ❌ npm not found!
) else (
    echo ✅ npm is installed
)

echo.
echo Checking MySQL...
mysql --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ MySQL not found in PATH
    echo Please install MySQL, XAMPP, or WAMP
) else (
    echo ✅ MySQL is available
)

echo.
echo Checking project files...
if exist "backend\package.json" (
    echo ✅ Backend files found
) else (
    echo ❌ Backend files missing
)

if exist "frontend\package.json" (
    echo ✅ Frontend files found
) else (
    echo ❌ Frontend files missing
)

if exist "database\mysql-schema.sql" (
    echo ✅ Database schema found
) else (
    echo ❌ Database schema missing
)

echo.
echo ========================================
echo System Check Complete
echo ========================================
echo.
pause
