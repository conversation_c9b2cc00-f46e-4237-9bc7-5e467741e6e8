const mongoose = require('mongoose');

const carSchema = new mongoose.Schema({
  plateNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true,
    match: /^[A-Z]{3}-\d{3}[A-Z]$/
  },
  carType: {
    type: String,
    required: true,
    enum: ['Sedan', 'SUV', 'Hatchback', 'Truck', 'Van', 'Coupe', 'Convertible']
  },
  carSize: {
    type: String,
    required: true,
    enum: ['Small', 'Medium', 'Large']
  },
  driverName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  phoneNumber: {
    type: String,
    required: true,
    trim: true,
    match: /^(\+250|0)?[7][0-9]{8}$/
  }
}, {
  timestamps: true
});

// Index for better search performance
carSchema.index({ plateNumber: 1 });
carSchema.index({ driverName: 1 });
carSchema.index({ carType: 1 });
carSchema.index({ carSize: 1 });

module.exports = mongoose.model('Car', carSchema);
