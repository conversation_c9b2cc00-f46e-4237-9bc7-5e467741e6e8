const express = require('express');
const { executeQuery, getOne } = require('../config/database');
const { authenticateUser, requireAdmin } = require('../middleware/auth');
const router = express.Router();

// Apply authentication to all routes
router.use(authenticateUser);

// GET /api/packages - Retrieve all service packages
router.get('/', async (req, res) => {
  try {
    const query = 'SELECT * FROM Package ORDER BY PackagePrice';
    const result = await executeQuery(query);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve packages'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get packages error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/packages/:id - Get specific package
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const query = 'SELECT * FROM Package WHERE PackageNumber = ?';
    const result = await getOne(query, [id]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve package'
      });
    }

    if (!result.data) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get package error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// POST /api/packages - Add new package (admin only)
router.post('/', requireAdmin, async (req, res) => {
  try {
    const { packageName, packageDescription, packagePrice } = req.body;

    // Validate required fields
    if (!packageName || !packagePrice) {
      return res.status(400).json({
        success: false,
        message: 'Package name and price are required'
      });
    }

    // Validate price is a positive number
    if (isNaN(packagePrice) || packagePrice <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Package price must be a positive number'
      });
    }

    const query = `
      INSERT INTO Package (PackageName, PackageDescription, PackagePrice) 
      VALUES (?, ?, ?)
    `;
    const result = await executeQuery(query, [packageName, packageDescription || '', packagePrice]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to add package'
      });
    }

    res.status(201).json({
      success: true,
      message: 'Package added successfully',
      data: { 
        packageNumber: result.data.insertId,
        packageName, 
        packageDescription, 
        packagePrice 
      }
    });

  } catch (error) {
    console.error('Add package error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// PUT /api/packages/:id - Update package details (admin only)
router.put('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { packageName, packageDescription, packagePrice } = req.body;

    // Validate required fields
    if (!packageName || !packagePrice) {
      return res.status(400).json({
        success: false,
        message: 'Package name and price are required'
      });
    }

    // Validate price is a positive number
    if (isNaN(packagePrice) || packagePrice <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Package price must be a positive number'
      });
    }

    // Check if package exists
    const existingPackage = await getOne('SELECT PackageNumber FROM Package WHERE PackageNumber = ?', [id]);
    if (!existingPackage.data) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    const query = `
      UPDATE Package 
      SET PackageName = ?, PackageDescription = ?, PackagePrice = ? 
      WHERE PackageNumber = ?
    `;
    const result = await executeQuery(query, [packageName, packageDescription || '', packagePrice, id]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to update package'
      });
    }

    res.json({
      success: true,
      message: 'Package updated successfully'
    });

  } catch (error) {
    console.error('Update package error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// DELETE /api/packages/:id - Delete package (admin only)
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if package exists
    const existingPackage = await getOne('SELECT PackageNumber FROM Package WHERE PackageNumber = ?', [id]);
    if (!existingPackage.data) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    // Check if package is being used in services
    const usageCheck = await getOne('SELECT RecordNumber FROM ServicePackage WHERE PackageNumber = ?', [id]);
    if (usageCheck.data) {
      return res.status(409).json({
        success: false,
        message: 'Cannot delete package that is being used in service records'
      });
    }

    const query = 'DELETE FROM Package WHERE PackageNumber = ?';
    const result = await executeQuery(query, [id]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to delete package'
      });
    }

    res.json({
      success: true,
      message: 'Package deleted successfully'
    });

  } catch (error) {
    console.error('Delete package error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
