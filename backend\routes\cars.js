const express = require('express');
const { executeQuery, getOne } = require('../config/database');
const { authenticateUser } = require('../middleware/auth');
const router = express.Router();

// Apply authentication to all routes
router.use(authenticateUser);

// GET /api/cars - Retrieve all cars
router.get('/', async (req, res) => {
  try {
    const query = 'SELECT * FROM Car ORDER BY PlateNumber';
    const result = await executeQuery(query);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve cars'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get cars error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/cars/:plateNumber - Get specific car details
router.get('/:plateNumber', async (req, res) => {
  try {
    const { plateNumber } = req.params;

    const query = 'SELECT * FROM Car WHERE PlateNumber = ?';
    const result = await getOne(query, [plateNumber]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve car'
      });
    }

    if (!result.data) {
      return res.status(404).json({
        success: false,
        message: 'Car not found'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get car error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// POST /api/cars - Add new car
router.post('/', async (req, res) => {
  try {
    const { plateNumber, carType, carSize, driverName, phoneNumber } = req.body;

    // Validate required fields
    if (!plateNumber || !carType || !carSize || !driverName || !phoneNumber) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Create new car
    const car = new Car({
      plateNumber: plateNumber.toUpperCase(),
      carType,
      carSize,
      driverName,
      phoneNumber
    });

    const savedCar = await car.save();

    res.status(201).json({
      success: true,
      message: 'Car added successfully',
      data: savedCar
    });

  } catch (error) {
    console.error('Add car error:', error);

    if (error.code === 11000) {
      return res.status(409).json({
        success: false,
        message: 'Car with this plate number already exists'
      });
    }

    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// PUT /api/cars/:plateNumber - Update car details
router.put('/:plateNumber', async (req, res) => {
  try {
    const { plateNumber } = req.params;
    const { carType, carSize, driverName, phoneNumber } = req.body;

    // Validate required fields
    if (!carType || !carSize || !driverName || !phoneNumber) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Check if car exists
    const existingCar = await getOne('SELECT PlateNumber FROM Car WHERE PlateNumber = ?', [plateNumber]);
    if (!existingCar.data) {
      return res.status(404).json({
        success: false,
        message: 'Car not found'
      });
    }

    const query = `
      UPDATE Car
      SET CarType = ?, CarSize = ?, DriverName = ?, PhoneNumber = ?
      WHERE PlateNumber = ?
    `;
    const result = await executeQuery(query, [carType, carSize, driverName, phoneNumber, plateNumber]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to update car'
      });
    }

    res.json({
      success: true,
      message: 'Car updated successfully'
    });

  } catch (error) {
    console.error('Update car error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// DELETE /api/cars/:plateNumber - Delete car record
router.delete('/:plateNumber', async (req, res) => {
  try {
    const { plateNumber } = req.params;

    // Check if car exists
    const existingCar = await getOne('SELECT PlateNumber FROM Car WHERE PlateNumber = ?', [plateNumber]);
    if (!existingCar.data) {
      return res.status(404).json({
        success: false,
        message: 'Car not found'
      });
    }

    const query = 'DELETE FROM Car WHERE PlateNumber = ?';
    const result = await executeQuery(query, [plateNumber]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to delete car'
      });
    }

    res.json({
      success: true,
      message: 'Car deleted successfully'
    });

  } catch (error) {
    console.error('Delete car error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
