import React, { useState, useEffect } from 'react';
import { Plus, FileText, Calendar, Car, Package } from 'lucide-react';
import { servicesAPI, carsAPI, packagesAPI, apiHelpers } from '../services/api';
import toast from 'react-hot-toast';

const ServiceRecording = () => {
  const [services, setServices] = useState([]);
  const [cars, setCars] = useState([]);
  const [packages, setPackages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState({
    serviceDate: apiHelpers.getTodayDate(),
    plateNumber: '',
    packageId: ''
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const [servicesResponse, carsResponse, packagesResponse] = await Promise.all([
        servicesAPI.getAll(),
        carsAPI.getAll(),
        packagesAPI.getAll()
      ]);
      
      setServices(servicesResponse.data.data);
      setCars(carsResponse.data.data);
      setPackages(packagesResponse.data.data);
    } catch (error) {
      toast.error('Failed to load data');
      console.error('Fetch data error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      await servicesAPI.create(formData);
      toast.success('Service recorded successfully');
      fetchData();
      setShowModal(false);
      setFormData({
        serviceDate: apiHelpers.getTodayDate(),
        plateNumber: '',
        packageId: ''
      });
    } catch (error) {
      toast.error('Failed to record service');
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-secondary-200 rounded w-1/4 mb-6"></div>
          <div className="card">
            <div className="card-body">
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-12 bg-secondary-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Service Recording</h1>
          <p className="text-secondary-600">Record car wash services and track service history</p>
        </div>
        <button
          onClick={() => setShowModal(true)}
          className="btn-primary"
        >
          <Plus className="h-4 w-4 mr-2" />
          Record New Service
        </button>
      </div>

      {/* Services Table */}
      <div className="card">
        <div className="card-body p-0">
          {services.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="table">
                <thead>
                  <tr>
                    <th>Service Date</th>
                    <th>Plate Number</th>
                    <th>Driver</th>
                    <th>Package</th>
                    <th>Price</th>
                    <th>Payment Status</th>
                  </tr>
                </thead>
                <tbody>
                  {services.map((service) => (
                    <tr key={service._id}>
                      <td>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-secondary-400" />
                          <span>{apiHelpers.formatDate(service.serviceDate)}</span>
                        </div>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2">
                          <Car className="h-4 w-4 text-secondary-400" />
                          <span className="font-medium">{service.plateNumber}</span>
                        </div>
                      </td>
                      <td>{service.carInfo?.driverName || 'N/A'}</td>
                      <td>
                        <div className="flex items-center space-x-2">
                          <Package className="h-4 w-4 text-secondary-400" />
                          <span>{service.packageName}</span>
                        </div>
                      </td>
                      <td className="font-medium">
                        {apiHelpers.formatCurrency(service.packagePrice)}
                      </td>
                      <td>
                        <span className={`badge ${
                          service.paymentStatus === 'Paid' ? 'badge-success' :
                          service.paymentStatus === 'Partial' ? 'badge-warning' :
                          'badge-danger'
                        }`}>
                          {service.paymentStatus}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-secondary-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-secondary-900 mb-2">No services recorded</h3>
              <p className="text-secondary-500 mb-6">
                Start by recording your first car wash service.
              </p>
              <button
                onClick={() => setShowModal(true)}
                className="btn-primary"
              >
                <Plus className="h-4 w-4 mr-2" />
                Record First Service
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6">
              <h2 className="text-xl font-bold text-secondary-900 mb-6">
                Record New Service
              </h2>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="label">Service Date</label>
                  <input
                    type="date"
                    className="input"
                    value={formData.serviceDate}
                    onChange={(e) => setFormData({...formData, serviceDate: e.target.value})}
                    required
                  />
                </div>

                <div>
                  <label className="label">Car</label>
                  <select
                    className="input"
                    value={formData.plateNumber}
                    onChange={(e) => setFormData({...formData, plateNumber: e.target.value})}
                    required
                  >
                    <option value="">Select a car</option>
                    {cars.map(car => (
                      <option key={car.plateNumber} value={car.plateNumber}>
                        {car.plateNumber} - {car.driverName}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="label">Service Package</label>
                  <select
                    className="input"
                    value={formData.packageId}
                    onChange={(e) => setFormData({...formData, packageId: e.target.value})}
                    required
                  >
                    <option value="">Select a package</option>
                    {packages.map(pkg => (
                      <option key={pkg._id} value={pkg._id}>
                        {pkg.packageName} - {apiHelpers.formatCurrency(pkg.packagePrice)}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="flex-1 btn-outline"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="flex-1 btn-primary"
                  >
                    Record Service
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServiceRecording;
