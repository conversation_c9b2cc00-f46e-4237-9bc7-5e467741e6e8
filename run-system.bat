@echo off
echo ========================================
echo SmartPark Car Wash Management System
echo ========================================
echo.

echo Starting Backend Server...
start "SmartPark Backend" cmd /k "cd backend && npm run dev"

echo Waiting 5 seconds for backend to start...
timeout /t 5 /nobreak > nul

echo Starting Frontend Server...
start "SmartPark Frontend" cmd /k "cd frontend && npm start"

echo.
echo ========================================
echo System Starting...
echo ========================================
echo.
echo Backend: http://localhost:5000
echo Frontend: http://localhost:3000
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.
echo Press any key to close this window...
pause > nul
