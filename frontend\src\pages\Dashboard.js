import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Car, 
  Package, 
  FileText, 
  CreditCard, 
  TrendingUp, 
  Calendar,
  Users,
  DollarSign,
  Activity,
  AlertCircle
} from 'lucide-react';
import { reportsAPI, servicesAPI, paymentsAPI, apiHelpers } from '../services/api';
import toast from 'react-hot-toast';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    todayStats: {
      TotalServices: 0,
      TotalRevenue: 0,
      TotalPayments: 0,
      OutstandingAmount: 0
    },
    recentServices: [],
    outstandingPayments: []
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      const today = apiHelpers.getTodayDate();
      
      const [dailyReportResponse, recentServicesResponse, outstandingResponse] = await Promise.all([
        reportsAPI.getDailyReport(today),
        servicesAPI.getAll(),
        paymentsAPI.getOutstanding()
      ]);

      setDashboardData({
        todayStats: dailyReportResponse.data.data.summary,
        recentServices: recentServicesResponse.data.data.slice(0, 5),
        outstandingPayments: outstandingResponse.data.data.slice(0, 5)
      });
    } catch (error) {
      toast.error('Failed to load dashboard data');
      console.error('Dashboard error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const quickActions = [
    {
      name: 'Add New Car',
      href: '/cars',
      icon: Car,
      color: 'bg-blue-500',
      description: 'Register a new vehicle'
    },
    {
      name: 'Record Service',
      href: '/services',
      icon: FileText,
      color: 'bg-green-500',
      description: 'Log a car wash service'
    },
    {
      name: 'Process Payment',
      href: '/payments',
      icon: CreditCard,
      color: 'bg-purple-500',
      description: 'Record payment received'
    },
    {
      name: 'View Reports',
      href: '/reports',
      icon: TrendingUp,
      color: 'bg-orange-500',
      description: 'Generate business reports'
    }
  ];

  const statsCards = [
    {
      name: "Today's Services",
      value: dashboardData.todayStats.TotalServices || 0,
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      name: "Today's Revenue",
      value: apiHelpers.formatCurrency(dashboardData.todayStats.TotalRevenue || 0),
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      name: "Payments Received",
      value: apiHelpers.formatCurrency(dashboardData.todayStats.TotalPayments || 0),
      icon: CreditCard,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      name: "Outstanding Amount",
      value: apiHelpers.formatCurrency(dashboardData.todayStats.OutstandingAmount || 0),
      icon: AlertCircle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-secondary-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card">
                <div className="card-body">
                  <div className="h-4 bg-secondary-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-secondary-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Dashboard</h1>
          <p className="text-secondary-600">
            Welcome to SmartPark Car Wash Management System
          </p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-secondary-500">
          <Calendar className="h-4 w-4" />
          <span>{apiHelpers.formatDate(new Date())}</span>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="card">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-secondary-600">
                      {stat.name}
                    </p>
                    <p className="text-2xl font-bold text-secondary-900">
                      {stat.value}
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-secondary-900">Quick Actions</h2>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Link
                  key={index}
                  to={action.href}
                  className="flex items-center space-x-3 p-4 rounded-lg border border-secondary-200 hover:border-primary-300 hover:bg-primary-50 transition-colors group"
                >
                  <div className={`p-2 rounded-lg ${action.color} group-hover:scale-110 transition-transform`}>
                    <Icon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium text-secondary-900 group-hover:text-primary-700">
                      {action.name}
                    </h3>
                    <p className="text-sm text-secondary-500">
                      {action.description}
                    </p>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Services */}
        <div className="card">
          <div className="card-header flex items-center justify-between">
            <h2 className="text-lg font-semibold text-secondary-900">Recent Services</h2>
            <Link to="/services" className="text-sm text-primary-600 hover:text-primary-700">
              View all
            </Link>
          </div>
          <div className="card-body">
            {dashboardData.recentServices.length > 0 ? (
              <div className="space-y-3">
                {dashboardData.recentServices.map((service) => (
                  <div key={service.RecordNumber} className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                    <div>
                      <p className="font-medium text-secondary-900">
                        {service.PlateNumber}
                      </p>
                      <p className="text-sm text-secondary-600">
                        {service.DriverName} • {service.PackageName}
                      </p>
                      <p className="text-xs text-secondary-500">
                        {apiHelpers.formatDate(service.ServiceDate)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-secondary-900">
                        {apiHelpers.formatCurrency(service.PackagePrice)}
                      </p>
                      <span className={`badge ${
                        service.PaymentStatus === 'Paid' ? 'badge-success' :
                        service.PaymentStatus === 'Partial' ? 'badge-warning' :
                        'badge-danger'
                      }`}>
                        {service.PaymentStatus}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-secondary-300 mx-auto mb-3" />
                <p className="text-secondary-500">No recent services</p>
              </div>
            )}
          </div>
        </div>

        {/* Outstanding Payments */}
        <div className="card">
          <div className="card-header flex items-center justify-between">
            <h2 className="text-lg font-semibold text-secondary-900">Outstanding Payments</h2>
            <Link to="/payments" className="text-sm text-primary-600 hover:text-primary-700">
              View all
            </Link>
          </div>
          <div className="card-body">
            {dashboardData.outstandingPayments.length > 0 ? (
              <div className="space-y-3">
                {dashboardData.outstandingPayments.map((payment) => (
                  <div key={payment.RecordNumber} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div>
                      <p className="font-medium text-secondary-900">
                        {payment.PlateNumber}
                      </p>
                      <p className="text-sm text-secondary-600">
                        {payment.DriverName}
                      </p>
                      <p className="text-xs text-secondary-500">
                        {apiHelpers.formatDate(payment.ServiceDate)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-orange-600">
                        {apiHelpers.formatCurrency(payment.OutstandingAmount)}
                      </p>
                      <p className="text-xs text-secondary-500">
                        of {apiHelpers.formatCurrency(payment.PackagePrice)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-secondary-300 mx-auto mb-3" />
                <p className="text-secondary-500">No outstanding payments</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
