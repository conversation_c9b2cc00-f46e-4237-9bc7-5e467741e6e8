@echo off
echo ========================================
echo Installing SmartPark Dependencies
echo ========================================
echo.

echo Installing Backend Dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo Error installing backend dependencies!
    echo Please check your internet connection and try again.
    pause
    exit /b 1
)

echo.
echo Installing Frontend Dependencies...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo Error installing frontend dependencies!
    echo Please check your internet connection and try again.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Dependencies Installed Successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Setup MySQL database (use setup-database.bat)
echo 2. Start backend (use start-backend.bat)
echo 3. Start frontend (use start-frontend.bat)
echo.
pause
