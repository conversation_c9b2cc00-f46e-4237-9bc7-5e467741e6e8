@echo off
echo ========================================
echo SmartPark Database Setup
echo ========================================
echo.

echo This script will help you set up the MySQL database.
echo.
echo Prerequisites:
echo 1. MySQL server must be running
echo 2. You need MySQL credentials (username/password)
echo.

set /p mysql_user="Enter MySQL username (default: root): "
if "%mysql_user%"=="" set mysql_user=root

set /p mysql_password="Enter MySQL password (press Enter if no password): "

echo.
echo Creating database and importing schema...
echo.

if "%mysql_password%"=="" (
    mysql -u %mysql_user% < database/mysql-schema.sql
) else (
    mysql -u %mysql_user% -p%mysql_password% < database/mysql-schema.sql
)

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo Database setup completed successfully!
    echo ========================================
    echo.
    echo Database: car_wash_management
    echo Sample data has been imported.
    echo.
    echo You can now start the application:
    echo 1. Run: start-backend.bat
    echo 2. Run: start-frontend.bat
    echo 3. Open: http://localhost:3000
    echo.
) else (
    echo.
    echo ========================================
    echo Database setup failed!
    echo ========================================
    echo.
    echo Please check:
    echo 1. MySQL server is running
    echo 2. Username and password are correct
    echo 3. MySQL is accessible from command line
    echo.
)

pause
