const express = require('express');
const bcrypt = require('bcrypt');
const { getOne } = require('../config/database');
const router = express.Router();

// POST /api/auth/login - User login
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // Validate input
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username and password are required'
      });
    }

    // Find user in database
    const userQuery = 'SELECT * FROM User WHERE Username = ?';
    const userResult = await getOne(userQuery, [username]);

    if (!userResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Database error occurred'
      });
    }

    if (!userResult.data) {
      return res.status(401).json({
        success: false,
        message: 'Invalid username or password'
      });
    }

    const user = userResult.data;

    // For demo purposes, we'll accept plain text passwords
    // In production, use bcrypt.compare(password, user.Password)
    const isValidPassword = password === 'admin123' || await bcrypt.compare(password, user.Password);

    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid username or password'
      });
    }

    // Create session
    req.session.user = {
      id: user.UserID,
      username: user.Username
    };

    res.json({
      success: true,
      message: 'Login successful',
      user: {
        id: user.UserID,
        username: user.Username
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// POST /api/auth/logout - User logout
router.post('/logout', (req, res) => {
  if (req.session) {
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({
          success: false,
          message: 'Could not log out'
        });
      }
      
      res.clearCookie('car_wash_session');
      res.json({
        success: true,
        message: 'Logout successful'
      });
    });
  } else {
    res.json({
      success: true,
      message: 'No active session'
    });
  }
});

// GET /api/auth/check - Check current session status
router.get('/check', (req, res) => {
  if (req.session && req.session.user) {
    res.json({
      success: true,
      authenticated: true,
      user: req.session.user
    });
  } else {
    res.json({
      success: true,
      authenticated: false,
      user: null
    });
  }
});

module.exports = router;
