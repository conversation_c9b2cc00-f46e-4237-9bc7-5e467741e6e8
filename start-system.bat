@echo off
echo ========================================
echo SmartPark Car Wash Management System
echo ========================================
echo.

echo Step 1: Installing Backend Dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo Error installing backend dependencies!
    pause
    exit /b 1
)

echo.
echo Step 2: Installing Frontend Dependencies...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo Error installing frontend dependencies!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation Complete!
echo ========================================
echo.
echo To start the system:
echo 1. Make sure MongoDB is running
echo 2. Open two terminals:
echo    Terminal 1: cd backend && npm run dev
echo    Terminal 2: cd frontend && npm start
echo.
echo Then open http://localhost:3000 in your browser
echo Login: admin / admin123
echo.
pause
