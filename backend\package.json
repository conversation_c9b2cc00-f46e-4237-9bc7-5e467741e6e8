{"name": "car-wash-backend", "version": "1.0.0", "description": "Backend API for Car Washing Sales Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["car-wash", "management", "api", "express", "mysql"], "author": "SmartPark Rwanda", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "express-session": "^1.17.3", "connect-mongo": "^5.1.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}