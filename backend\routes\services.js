const express = require('express');
const { executeQuery, getOne } = require('../config/database');
const { authenticateUser } = require('../middleware/auth');
const router = express.Router();

// Apply authentication to all routes
router.use(authenticateUser);

// GET /api/services - Retrieve all service records
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT 
        sp.RecordNumber,
        sp.ServiceDate,
        sp.PlateNumber,
        c.Driver<PERSON>ame,
        c.PhoneNumber,
        c.CarType,
        c.CarSize,
        p.PackageName,
        p.PackagePrice,
        COALESCE(pay.AmountPaid, 0) as AmountPaid,
        CASE 
          WHEN pay.AmountPaid >= p.PackagePrice THEN 'Paid'
          WHEN pay.AmountPaid > 0 THEN 'Partial'
          ELSE 'Unpaid'
        END as PaymentStatus
      FROM ServicePackage sp
      JOIN Car c ON sp.PlateNumber = c.PlateNumber
      JOIN Package p ON sp.PackageNumber = p.PackageNumber
      LEFT JOIN Payment pay ON sp.RecordNumber = pay.RecordNumber
      ORDER BY sp.ServiceDate DESC, sp.RecordNumber DESC
    `;
    
    const result = await executeQuery(query);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve services'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get services error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/services/:id - Get specific service record
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const query = `
      SELECT 
        sp.RecordNumber,
        sp.ServiceDate,
        sp.PlateNumber,
        sp.PackageNumber,
        c.DriverName,
        c.PhoneNumber,
        c.CarType,
        c.CarSize,
        p.PackageName,
        p.PackageDescription,
        p.PackagePrice,
        COALESCE(pay.AmountPaid, 0) as AmountPaid,
        pay.PaymentDate,
        CASE 
          WHEN pay.AmountPaid >= p.PackagePrice THEN 'Paid'
          WHEN pay.AmountPaid > 0 THEN 'Partial'
          ELSE 'Unpaid'
        END as PaymentStatus
      FROM ServicePackage sp
      JOIN Car c ON sp.PlateNumber = c.PlateNumber
      JOIN Package p ON sp.PackageNumber = p.PackageNumber
      LEFT JOIN Payment pay ON sp.RecordNumber = pay.RecordNumber
      WHERE sp.RecordNumber = ?
    `;
    
    const result = await getOne(query, [id]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve service'
      });
    }

    if (!result.data) {
      return res.status(404).json({
        success: false,
        message: 'Service record not found'
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get service error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// POST /api/services - Record new service
router.post('/', async (req, res) => {
  try {
    const { serviceDate, plateNumber, packageNumber } = req.body;

    // Validate required fields
    if (!serviceDate || !plateNumber || !packageNumber) {
      return res.status(400).json({
        success: false,
        message: 'Service date, plate number, and package number are required'
      });
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(serviceDate)) {
      return res.status(400).json({
        success: false,
        message: 'Service date must be in YYYY-MM-DD format'
      });
    }

    // Check if car exists
    const carExists = await getOne('SELECT PlateNumber FROM Car WHERE PlateNumber = ?', [plateNumber]);
    if (!carExists.data) {
      return res.status(404).json({
        success: false,
        message: 'Car not found. Please register the car first.'
      });
    }

    // Check if package exists
    const packageExists = await getOne('SELECT PackageNumber FROM Package WHERE PackageNumber = ?', [packageNumber]);
    if (!packageExists.data) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    const query = `
      INSERT INTO ServicePackage (ServiceDate, PlateNumber, PackageNumber) 
      VALUES (?, ?, ?)
    `;
    const result = await executeQuery(query, [serviceDate, plateNumber, packageNumber]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to record service'
      });
    }

    res.status(201).json({
      success: true,
      message: 'Service recorded successfully',
      data: { 
        recordNumber: result.data.insertId,
        serviceDate, 
        plateNumber, 
        packageNumber 
      }
    });

  } catch (error) {
    console.error('Add service error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// PUT /api/services/:id - Update service record
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { serviceDate, plateNumber, packageNumber } = req.body;

    // Validate required fields
    if (!serviceDate || !plateNumber || !packageNumber) {
      return res.status(400).json({
        success: false,
        message: 'Service date, plate number, and package number are required'
      });
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(serviceDate)) {
      return res.status(400).json({
        success: false,
        message: 'Service date must be in YYYY-MM-DD format'
      });
    }

    // Check if service exists
    const serviceExists = await getOne('SELECT RecordNumber FROM ServicePackage WHERE RecordNumber = ?', [id]);
    if (!serviceExists.data) {
      return res.status(404).json({
        success: false,
        message: 'Service record not found'
      });
    }

    // Check if car exists
    const carExists = await getOne('SELECT PlateNumber FROM Car WHERE PlateNumber = ?', [plateNumber]);
    if (!carExists.data) {
      return res.status(404).json({
        success: false,
        message: 'Car not found'
      });
    }

    // Check if package exists
    const packageExists = await getOne('SELECT PackageNumber FROM Package WHERE PackageNumber = ?', [packageNumber]);
    if (!packageExists.data) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    const query = `
      UPDATE ServicePackage 
      SET ServiceDate = ?, PlateNumber = ?, PackageNumber = ? 
      WHERE RecordNumber = ?
    `;
    const result = await executeQuery(query, [serviceDate, plateNumber, packageNumber, id]);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to update service'
      });
    }

    res.json({
      success: true,
      message: 'Service updated successfully'
    });

  } catch (error) {
    console.error('Update service error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
