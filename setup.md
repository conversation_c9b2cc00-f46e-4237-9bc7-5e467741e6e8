# SmartPark Car Wash Management System - Setup Guide

## Quick Start Instructions

### Prerequisites
1. **Node.js** (v18 or higher) - [Download here](https://nodejs.org/)
2. **MongoDB** (v5 or higher) - [Download here](https://www.mongodb.com/try/download/community)
3. **Git** (optional) - [Download here](https://git-scm.com/)

### Step 1: Install MongoDB
1. Download and install MongoDB Community Edition
2. Start MongoDB service:
   - **Windows**: `net start MongoDB` (run as administrator)
   - **macOS**: `brew services start mongodb-community`
   - **Linux**: `sudo systemctl start mongod`

### Step 2: Setup Backend
```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Start the backend server
npm run dev
```

The backend will run on `http://localhost:5000`

### Step 3: Setup Frontend
```bash
# Open a new terminal and navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start the frontend development server
npm start
```

The frontend will run on `http://localhost:3000`

### Step 4: Initialize Database (Optional)
The application will work with an empty database, but you can add sample data:

1. Open MongoDB Compass or mongosh
2. Connect to `mongodb://localhost:27017`
3. Create database `car_wash_management`
4. Run the commands from `database/mongodb-schema.js`

### Step 5: Access the Application
1. Open your browser and go to `http://localhost:3000`
2. Login with:
   - **Username**: `admin`
   - **Password**: `admin123`

## Troubleshooting

### MongoDB Connection Issues
- Ensure MongoDB service is running
- Check if port 27017 is available
- Verify MongoDB installation

### Port Conflicts
- Backend uses port 5000
- Frontend uses port 3000
- Change ports in `.env` files if needed

### Dependencies Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules
npm install
```

## Production Deployment

### Environment Variables
Create `.env` files with production values:

**Backend (.env)**:
```env
MONGODB_URI=mongodb://localhost:27017/car_wash_management
PORT=5000
NODE_ENV=production
SESSION_SECRET=your_secure_session_secret_here
```

**Frontend (.env)**:
```env
REACT_APP_API_URL=http://your-domain.com/api
```

### Build and Deploy
```bash
# Build frontend
cd frontend
npm run build

# Start backend in production
cd ../backend
npm start
```

## Features Overview

### 🚗 Car Management
- Register vehicles with plate numbers, types, and driver information
- Search and filter cars
- Edit and delete car records

### 📦 Package Management
- Create service packages with pricing
- Manage package descriptions and prices
- Admin-only access for package modifications

### 📝 Service Recording
- Record car wash services
- Link services to cars and packages
- Track service dates and details

### 💳 Payment Processing
- Record payments for services
- Track outstanding amounts
- Multiple payment methods support

### 📊 Reports & Analytics
- Daily service reports
- Payment summaries
- Package performance analytics

### 🧾 Bill Generation
- Generate detailed service bills
- Print-friendly format
- Professional invoice layout

## Default Data

The system includes sample data:
- 5 service packages (Basic, Premium, Deluxe, Express, VIP)
- 5 sample cars with different types
- Sample service records and payments

## Support

For technical support:
- Check the README.md for detailed documentation
- Review the troubleshooting section above
- Contact: <EMAIL>

---

**SmartPark Car Wash Management System** - Efficient car wash business management made simple.
